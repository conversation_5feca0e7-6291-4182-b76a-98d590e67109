#!/usr/bin/env python3
"""
Test script for F5-TTS Ray Serve integration.
This script tests the deployment, scaling, and performance of the Ray Serve setup.
"""

import asyncio
import aiohttp
import time
import logging
import sys
import os
from pathlib import Path
import tempfile
import soundfile as sf
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import statistics

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class F5TTSRayServeTest:
    def __init__(self, fastapi_url="http://localhost:8080", ray_serve_url="http://localhost:8000"):
        self.fastapi_url = fastapi_url
        self.ray_serve_url = ray_serve_url
        self.test_results = {}
    
    def create_test_audio(self, duration=2.0, sample_rate=24000):
        """Create a test audio file for testing."""
        # Generate a simple sine wave
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 note
        audio = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        sf.write(temp_file.name, audio, sample_rate)
        return temp_file.name
    
    async def test_health_checks(self):
        """Test health check endpoints."""
        logger.info("Testing health check endpoints...")
        
        async with aiohttp.ClientSession() as session:
            # Test FastAPI health
            try:
                async with session.get(f"{self.fastapi_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"FastAPI health: {data['status']}")
                        logger.info(f"Ray Serve enabled: {data.get('ray_serve_enabled', False)}")
                        self.test_results["fastapi_health"] = True
                    else:
                        logger.error(f"FastAPI health check failed: {response.status}")
                        self.test_results["fastapi_health"] = False
            except Exception as e:
                logger.error(f"FastAPI health check error: {e}")
                self.test_results["fastapi_health"] = False
            
            # Test Ray Serve health
            try:
                async with session.get(f"{self.ray_serve_url}/f5tts/health_check") as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Ray Serve health: {data['status']}")
                        self.test_results["ray_serve_health"] = True
                    else:
                        logger.error(f"Ray Serve health check failed: {response.status}")
                        self.test_results["ray_serve_health"] = False
            except Exception as e:
                logger.error(f"Ray Serve health check error: {e}")
                self.test_results["ray_serve_health"] = False
    
    async def test_single_synthesis(self):
        """Test a single synthesis request."""
        logger.info("Testing single synthesis request...")
        
        # Create test audio
        test_audio_path = self.create_test_audio()
        
        try:
            async with aiohttp.ClientSession() as session:
                data = aiohttp.FormData()
                data.add_field('ref_audio', open(test_audio_path, 'rb'), filename='test.wav')
                data.add_field('ref_text', 'This is a test reference.')
                data.add_field('gen_text', 'Hello, this is a test synthesis.')
                data.add_field('model_type', 'F5-TTS_v1')
                data.add_field('nfe_step', '16')  # Faster for testing
                
                start_time = time.time()
                async with session.post(f"{self.fastapi_url}/synthesize/", data=data) as response:
                    end_time = time.time()
                    
                    if response.status == 200:
                        # Save the response audio
                        output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
                        with open(output_path, 'wb') as f:
                            f.write(await response.read())
                        
                        processing_time = end_time - start_time
                        logger.info(f"Single synthesis successful in {processing_time:.2f}s")
                        logger.info(f"Output saved to: {output_path}")
                        
                        self.test_results["single_synthesis"] = {
                            "success": True,
                            "processing_time": processing_time,
                            "output_path": output_path
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"Single synthesis failed: {response.status} - {error_text}")
                        self.test_results["single_synthesis"] = {"success": False, "error": error_text}
        
        finally:
            # Clean up test audio
            try:
                os.unlink(test_audio_path)
            except:
                pass
    
    async def test_concurrent_requests(self, num_requests=5):
        """Test concurrent synthesis requests."""
        logger.info(f"Testing {num_requests} concurrent synthesis requests...")
        
        # Create test audio
        test_audio_path = self.create_test_audio()
        
        async def make_request(session, request_id):
            """Make a single synthesis request."""
            try:
                data = aiohttp.FormData()
                data.add_field('ref_audio', open(test_audio_path, 'rb'), filename=f'test_{request_id}.wav')
                data.add_field('ref_text', f'Test reference {request_id}.')
                data.add_field('gen_text', f'Concurrent test request number {request_id}.')
                data.add_field('model_type', 'F5-TTS_v1')
                data.add_field('nfe_step', '16')  # Faster for testing
                
                start_time = time.time()
                async with session.post(f"{self.fastapi_url}/synthesize/", data=data) as response:
                    end_time = time.time()
                    
                    processing_time = end_time - start_time
                    success = response.status == 200
                    
                    if success:
                        logger.info(f"Request {request_id} completed in {processing_time:.2f}s")
                    else:
                        error_text = await response.text()
                        logger.error(f"Request {request_id} failed: {response.status} - {error_text}")
                    
                    return {
                        "request_id": request_id,
                        "success": success,
                        "processing_time": processing_time,
                        "status_code": response.status
                    }
            except Exception as e:
                logger.error(f"Request {request_id} error: {e}")
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": str(e)
                }
        
        try:
            # Make concurrent requests
            async with aiohttp.ClientSession() as session:
                tasks = [make_request(session, i) for i in range(num_requests)]
                results = await asyncio.gather(*tasks)
            
            # Analyze results
            successful_requests = [r for r in results if r.get("success", False)]
            failed_requests = [r for r in results if not r.get("success", False)]
            
            if successful_requests:
                processing_times = [r["processing_time"] for r in successful_requests]
                avg_time = statistics.mean(processing_times)
                min_time = min(processing_times)
                max_time = max(processing_times)
                
                logger.info(f"Concurrent test results:")
                logger.info(f"  Successful: {len(successful_requests)}/{num_requests}")
                logger.info(f"  Average time: {avg_time:.2f}s")
                logger.info(f"  Min time: {min_time:.2f}s")
                logger.info(f"  Max time: {max_time:.2f}s")
                
                self.test_results["concurrent_requests"] = {
                    "total_requests": num_requests,
                    "successful_requests": len(successful_requests),
                    "failed_requests": len(failed_requests),
                    "average_time": avg_time,
                    "min_time": min_time,
                    "max_time": max_time
                }
            else:
                logger.error("All concurrent requests failed!")
                self.test_results["concurrent_requests"] = {
                    "total_requests": num_requests,
                    "successful_requests": 0,
                    "failed_requests": len(failed_requests)
                }
        
        finally:
            # Clean up test audio
            try:
                os.unlink(test_audio_path)
            except:
                pass
    
    async def test_caching(self):
        """Test result caching functionality."""
        logger.info("Testing caching functionality...")
        
        # Create test audio
        test_audio_path = self.create_test_audio()
        
        try:
            async with aiohttp.ClientSession() as session:
                # Prepare identical requests
                data1 = aiohttp.FormData()
                data1.add_field('ref_audio', open(test_audio_path, 'rb'), filename='cache_test.wav')
                data1.add_field('ref_text', 'Cache test reference.')
                data1.add_field('gen_text', 'This is a cache test.')
                data1.add_field('model_type', 'F5-TTS_v1')
                data1.add_field('seed', '42')  # Fixed seed for consistent results
                
                # First request (should be processed)
                start_time1 = time.time()
                async with session.post(f"{self.fastapi_url}/synthesize/", data=data1) as response1:
                    end_time1 = time.time()
                    time1 = end_time1 - start_time1
                    success1 = response1.status == 200
                
                if success1:
                    logger.info(f"First request completed in {time1:.2f}s")
                    
                    # Second identical request (should be faster if cached)
                    data2 = aiohttp.FormData()
                    data2.add_field('ref_audio', open(test_audio_path, 'rb'), filename='cache_test.wav')
                    data2.add_field('ref_text', 'Cache test reference.')
                    data2.add_field('gen_text', 'This is a cache test.')
                    data2.add_field('model_type', 'F5-TTS_v1')
                    data2.add_field('seed', '42')  # Same seed
                    
                    start_time2 = time.time()
                    async with session.post(f"{self.fastapi_url}/synthesize/", data=data2) as response2:
                        end_time2 = time.time()
                        time2 = end_time2 - start_time2
                        success2 = response2.status == 200
                    
                    if success2:
                        logger.info(f"Second request completed in {time2:.2f}s")
                        speedup = time1 / time2 if time2 > 0 else 1
                        logger.info(f"Speedup: {speedup:.2f}x")
                        
                        self.test_results["caching"] = {
                            "first_request_time": time1,
                            "second_request_time": time2,
                            "speedup": speedup,
                            "cache_effective": speedup > 1.5  # Expect significant speedup
                        }
                    else:
                        logger.error("Second request failed")
                        self.test_results["caching"] = {"success": False, "error": "Second request failed"}
                else:
                    logger.error("First request failed")
                    self.test_results["caching"] = {"success": False, "error": "First request failed"}
        
        finally:
            # Clean up test audio
            try:
                os.unlink(test_audio_path)
            except:
                pass
    
    def print_summary(self):
        """Print test summary."""
        logger.info("\n" + "="*50)
        logger.info("F5-TTS Ray Serve Test Summary")
        logger.info("="*50)
        
        for test_name, result in self.test_results.items():
            logger.info(f"\n{test_name.upper()}:")
            if isinstance(result, dict):
                for key, value in result.items():
                    logger.info(f"  {key}: {value}")
            else:
                logger.info(f"  Result: {result}")
        
        # Overall assessment
        health_ok = self.test_results.get("fastapi_health", False) and self.test_results.get("ray_serve_health", False)
        synthesis_ok = self.test_results.get("single_synthesis", {}).get("success", False)
        concurrent_ok = self.test_results.get("concurrent_requests", {}).get("successful_requests", 0) > 0
        
        logger.info(f"\nOVERALL STATUS:")
        logger.info(f"  Health Checks: {'✓' if health_ok else '✗'}")
        logger.info(f"  Single Synthesis: {'✓' if synthesis_ok else '✗'}")
        logger.info(f"  Concurrent Requests: {'✓' if concurrent_ok else '✗'}")
        
        if health_ok and synthesis_ok and concurrent_ok:
            logger.info(f"  🎉 All tests passed! Ray Serve integration is working correctly.")
        else:
            logger.info(f"  ❌ Some tests failed. Check the logs for details.")


async def main():
    """Main test function."""
    logger.info("Starting F5-TTS Ray Serve integration tests...")
    
    tester = F5TTSRayServeTest()
    
    # Run tests
    await tester.test_health_checks()
    await tester.test_single_synthesis()
    await tester.test_concurrent_requests(num_requests=3)
    await tester.test_caching()
    
    # Print summary
    tester.print_summary()


if __name__ == "__main__":
    asyncio.run(main())
