#!/usr/bin/env python3
"""
Ray Serve startup script for F5-TTS API.
This script initializes Ray cluster and deploys the F5-TTS service.
"""

import os
import sys
import logging
import argparse
import time
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import ray
from ray import serve
from f5_tts_api.ray_serve_deployment import create_f5tts_deployment

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Start F5-TTS Ray Serve deployment")
    
    parser.add_argument(
        "--ray-address",
        type=str,
        default=None,
        help="Ray cluster address (default: start local cluster)"
    )
    
    parser.add_argument(
        "--num-cpus",
        type=int,
        default=None,
        help="Number of CPUs for Ray cluster (default: auto-detect)"
    )
    
    parser.add_argument(
        "--num-gpus",
        type=int,
        default=None,
        help="Number of GPUs for Ray cluster (default: auto-detect)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port for Ray Serve HTTP server (default: 8000)"
    )
    
    parser.add_argument(
        "--detached",
        action="store_true",
        help="Run Ray Serve in detached mode"
    )
    
    parser.add_argument(
        "--route-prefix",
        type=str,
        default="/f5tts",
        help="Route prefix for the F5-TTS service (default: /f5tts)"
    )
    
    return parser.parse_args()


def initialize_ray_cluster(args):
    """Initialize Ray cluster with specified configuration."""
    logger.info("Initializing Ray cluster...")
    
    ray_config = {}
    
    if args.ray_address:
        logger.info(f"Connecting to existing Ray cluster at {args.ray_address}")
        ray_config["address"] = args.ray_address
    else:
        logger.info("Starting local Ray cluster")
        if args.num_cpus:
            ray_config["num_cpus"] = args.num_cpus
        if args.num_gpus:
            ray_config["num_gpus"] = args.num_gpus
    
    # Initialize Ray
    if not ray.is_initialized():
        ray.init(**ray_config)
    
    # Print cluster information
    logger.info(f"Ray cluster initialized successfully")
    try:
        logger.info(f"Ray dashboard: http://127.0.0.1:8265")
        logger.info(f"Available resources: {ray.cluster_resources()}")
    except Exception as e:
        logger.warning(f"Could not get cluster info: {e}")


def deploy_f5tts_service(args):
    """Deploy the F5-TTS service using Ray Serve."""
    logger.info("Deploying F5-TTS service...")
    
    # Start Ray Serve
    serve.start(
        detached=args.detached,
        http_options={"host": "0.0.0.0", "port": args.port}
    )
    
    # Create and deploy the F5-TTS service
    deployment = create_f5tts_deployment()
    
    # Deploy with specified route prefix
    serve.run(
        deployment,
        name="f5tts-service",
        route_prefix=args.route_prefix
    )
    
    logger.info(f"F5-TTS service deployed successfully!")
    logger.info(f"Service endpoint: http://localhost:{args.port}{args.route_prefix}")
    logger.info(f"Health check: http://localhost:{args.port}{args.route_prefix}/health_check")


def wait_for_deployment_ready(args, timeout=300):
    """Wait for the deployment to be ready."""
    logger.info("Waiting for deployment to be ready...")
    
    import requests
    import time
    
    health_url = f"http://localhost:{args.port}{args.route_prefix}/health_check"
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                if health_data.get("status") == "healthy":
                    logger.info("Deployment is ready and healthy!")
                    return True
        except Exception as e:
            logger.debug(f"Health check failed: {e}")
        
        time.sleep(5)
    
    logger.error(f"Deployment not ready after {timeout} seconds")
    return False


def main():
    """Main function to start Ray Serve with F5-TTS deployment."""
    args = parse_args()
    
    try:
        # Initialize Ray cluster
        initialize_ray_cluster(args)
        
        # Deploy F5-TTS service
        deploy_f5tts_service(args)
        
        # Wait for deployment to be ready
        if wait_for_deployment_ready(args):
            logger.info("F5-TTS Ray Serve deployment started successfully!")
            logger.info("Service is ready to handle requests.")
            
            if not args.detached:
                logger.info("Press Ctrl+C to stop the service...")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("Shutting down Ray Serve...")
                    serve.shutdown()
                    ray.shutdown()
        else:
            logger.error("Failed to start F5-TTS service")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error starting F5-TTS Ray Serve: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
