import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
import asyncio
import time

# Ray Serve imports
import ray
from ray import serve
from ray.serve.handle import DeploymentHandle

# Import F5TTSWrapper (always needed)
from f5_tts_api.f5tts_wrapper import F5TTSWrapper

# Try to import Ray Serve deployment
try:
    from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
    RAY_SERVE_AVAILABLE = True
except ImportError:
    RAY_SERVE_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS Web API with Ray Serve")

# Initialize TTS service (Ray Serve or direct wrapper)
tts_service = None
use_ray_serve = False

def initialize_tts_service():
    """Initialize TTS service with Ray Serve if available, otherwise use direct wrapper."""
    global tts_service, use_ray_serve

    if RAY_SERVE_AVAILABLE:
        try:
            # Try to connect to existing Ray Serve deployment
            if not ray.is_initialized():
                ray.init(address="auto", ignore_reinit_error=True)

            # Check if Ray Serve is running and deployment exists
            try:
                from ray.serve._private.api import _get_global_client
                client = _get_global_client()
                if client is not None:
                    # Try to get handle to existing deployment
                    tts_service = serve.get_app_handle("f5tts-service")
                    use_ray_serve = True
                    logger.info("Connected to Ray Serve F5-TTS deployment")
                else:
                    raise Exception("Ray Serve not running")
            except:
                # Ray Serve not running, fall back
                raise Exception("Ray Serve deployment not found")

        except Exception as e:
            logger.warning(f"Failed to connect to Ray Serve: {e}")
            logger.info("Falling back to direct F5TTSWrapper")
            tts_service = F5TTSWrapper(model_type="F5-TTS_v1")
            use_ray_serve = False
    else:
        logger.info("Ray Serve not available, using direct F5TTSWrapper")
        tts_service = F5TTSWrapper(model_type="F5-TTS_v1")
        use_ray_serve = False

# Initialize the service
initialize_tts_service()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

async def synthesize_with_ray_serve(request_data: dict) -> dict:
    """Synthesize speech using Ray Serve deployment."""
    try:
        result = await tts_service.synthesize.remote(request_data)
        return result
    except Exception as e:
        logger.error(f"Ray Serve synthesis failed: {e}")
        raise

def synthesize_with_direct_wrapper(request_data: dict) -> dict:
    """Synthesize speech using direct F5TTSWrapper."""
    try:
        (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts_service.infer(
            ref_audio_orig=request_data["ref_audio_path"],
            ref_text=request_data.get("ref_text"),
            gen_text=request_data["gen_text"],
            model_type=request_data.get("model_type", "F5-TTS_v1"),
            remove_silence=request_data.get("remove_silence", False),
            seed=request_data.get("seed", -1),
            cross_fade_duration=request_data.get("cross_fade_duration", 0.15),
            nfe_step=request_data.get("nfe_step", 32),
            speed=request_data.get("speed", 1.0),
            show_info=logger.info,
        )

        # Save output audio
        output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(output_path, audio_data, sample_rate)

        return {
            "success": True,
            "output_path": output_path,
            "sample_rate": sample_rate,
            "spectrogram_path": spectrogram_path,
            "processed_ref_text": processed_ref_text,
            "used_seed": used_seed,
            "processing_time": 0,  # Not tracked in direct mode
            "from_cache": False
        }
    except Exception as e:
        logger.error(f"Direct wrapper synthesis failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS with Ray Serve or direct wrapper.
    Automatically uses Ray Serve if available, otherwise falls back to direct wrapper.

    Args:
        ref_audio: Reference audio file
        ref_text: Reference text (if empty, will be transcribed)
        gen_text: Text to generate
        model_type: Model type to use ("F5-TTS_v1" or "E2-TTS")
        remove_silence: Whether to remove silence from output
        seed: Random seed (-1 for random)
        cross_fade_duration: Cross-fade duration between segments
        nfe_step: Number of denoising steps
        speed: Speed multiplier
    """
    request_id = f"req_{int(time.time() * 1000)}"
    logger.info(f"Endpoint '/synthesize/' called. Request ID: {request_id}")
    logger.info("Received file: %s", ref_audio.filename)
    logger.info("ref_text: %s", ref_text)
    logger.info("gen_text preview: %s", gen_text[:50])
    logger.info("Parameters - model_type: %s, remove_silence: %s, seed: %s, cross_fade_duration: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, cross_fade_duration, nfe_step, speed)
    logger.info(f"Using Ray Serve: {use_ray_serve}")

    tmp_audio_path = None
    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved reference audio to: %s", tmp_audio_path)

        # Prepare request data
        request_data = {
            "request_id": request_id,
            "ref_audio_path": tmp_audio_path,
            "ref_text": ref_text,
            "gen_text": gen_text,
            "model_type": model_type,
            "remove_silence": remove_silence,
            "seed": seed,
            "cross_fade_duration": cross_fade_duration,
            "nfe_step": nfe_step,
            "speed": speed,
        }

        # Use Ray Serve or direct wrapper
        if use_ray_serve:
            result = await synthesize_with_ray_serve(request_data)
        else:
            result = synthesize_with_direct_wrapper(request_data)

        # Check if synthesis was successful
        if not result.get("success", False):
            error_msg = result.get("error", "Unknown synthesis error")
            logger.error(f"Synthesis failed: {error_msg}")
            raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {error_msg}")

        # Log success information
        logger.info(f"Synthesis successful for request {request_id}")
        if "processing_time" in result:
            logger.info(f"Processing time: {result['processing_time']:.2f}s")
        if "from_cache" in result:
            logger.info(f"Result from cache: {result['from_cache']}")
        if "used_seed" in result:
            logger.info(f"Used seed: {result['used_seed']}")
        if "processed_ref_text" in result:
            logger.info(f"Processed ref_text: {result['processed_ref_text']}")

        # Get output path
        output_path = result.get("output_path")
        if not output_path:
            # For Ray Serve, we might need to create the output file from the result
            if "audio_data" in result and "sample_rate" in result:
                output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
                sf.write(output_path, result["audio_data"], result["sample_rate"])
            else:
                raise HTTPException(status_code=500, detail="No output audio generated")

        logger.info("Generated speech saved to: %s", output_path)

        # Clean up temporary reference audio file
        try:
            if tmp_audio_path:
                os.unlink(tmp_audio_path)
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return FileResponse(output_path, media_type="audio/wav", filename="output.wav")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error during speech synthesis: %s", str(e))
        # Clean up temporary files on error
        try:
            if tmp_audio_path:
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")

@app.get("/")
def root():
    logger.info("Endpoint '/' called.")
    return {
        "message": "Welcome to the F5-TTS Web API with Ray Serve",
        "version": "3.0",
        "ray_serve_enabled": use_ray_serve,
        "features": [
            "Ray Serve integration for scalable deployment",
            "GPU-accelerated inference with 5 CPU cores",
            "Horizontal scaling and load balancing",
            "Request caching and performance optimization",
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Silence removal",
            "Spectrogram generation",
            "Concurrent request handling"
        ],
        "endpoints": {
            "/synthesize/": "Enhanced synthesis with Ray Serve scaling",
            "/health": "Service health check",
            "/": "API information"
        },
        "performance": {
            "gpu_allocation": "1 GPU (cuda:0)",
            "cpu_cores": 5,
            "max_concurrent_requests": 10,
            "auto_scaling": "1-3 replicas",
            "caching_enabled": use_ray_serve
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for the FastAPI service."""
    logger.info("Health check endpoint called.")

    health_status = {
        "status": "healthy",
        "timestamp": time.time(),
        "ray_serve_enabled": use_ray_serve,
        "service_type": "Ray Serve" if use_ray_serve else "Direct Wrapper"
    }

    # If using Ray Serve, check the deployment health
    if use_ray_serve:
        try:
            ray_health = await tts_service.health_check.remote()
            health_status["ray_serve_health"] = ray_health
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["ray_serve_error"] = str(e)

    return health_status
