import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException, Response
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
import asyncio
import time
import torch

# Ray Serve imports
import ray
from ray import serve
from ray.serve.handle import DeploymentHandle

# Import F5TTSWrapper (always needed)
from f5_tts_api.f5tts_wrapper import F5TTSWrapper

# Try to import Ray Serve deployment
try:
    from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
    RAY_SERVE_AVAILABLE = True
except ImportError:
    RAY_SERVE_AVAILABLE = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS Web API with Ray Serve")

# Add CORS middleware for better performance
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize TTS service (Ray Serve or direct wrapper)
tts_service = None
use_ray_serve = False

# Performance optimizations
torch.set_num_threads(4)  # Optimize CPU usage
if torch.cuda.is_available():
    torch.backends.cudnn.benchmark = True  # Optimize CUDA performance

def initialize_tts_service():
    """Initialize TTS service with Ray Serve if available, otherwise use direct wrapper."""
    global tts_service, use_ray_serve

    if RAY_SERVE_AVAILABLE:
        try:
            # Try to connect to existing Ray Serve deployment
            if not ray.is_initialized():
                ray.init(address="auto", ignore_reinit_error=True)

            # Check if Ray Serve is running and deployment exists
            try:
                from ray.serve._private.api import _get_global_client
                client = _get_global_client()
                if client is not None:
                    # Try to get handle to existing deployment
                    tts_service = serve.get_app_handle("f5tts-service")
                    use_ray_serve = True
                    logger.info("Connected to Ray Serve F5-TTS deployment")
                else:
                    raise Exception("Ray Serve not running")
            except:
                # Ray Serve not running, fall back
                raise Exception("Ray Serve deployment not found")

        except Exception as e:
            logger.warning(f"Failed to connect to Ray Serve: {e}")
            logger.info("Falling back to direct F5TTSWrapper")
            tts_service = F5TTSWrapper(model_type="F5-TTS_v1")
            use_ray_serve = False
    else:
        logger.info("Ray Serve not available, using direct F5TTSWrapper")
        tts_service = F5TTSWrapper(model_type="F5-TTS_v1")
        use_ray_serve = False

# Initialize the service
initialize_tts_service()

@app.middleware("http")
async def add_latency_header(request: Request, call_next):
    start_time = time.time()
    logger.info("Incoming request: %s %s", request.method, request.url)

    response = await call_next(request)

    # Calculate latency
    process_time = time.time() - start_time
    latency_ms = round(process_time * 1000, 2)

    # Add latency headers
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-Latency-Ms"] = str(latency_ms)
    response.headers["X-Ray-Serve-Enabled"] = str(use_ray_serve)

    logger.info("Completed request with status code: %d, latency: %sms",
                response.status_code, latency_ms)
    return response

async def synthesize_with_ray_serve(request_data: dict) -> dict:
    """Synthesize speech using Ray Serve deployment."""
    try:
        result = await tts_service.synthesize.remote(request_data)
        return result
    except Exception as e:
        logger.error(f"Ray Serve synthesis failed: {e}")
        raise

def synthesize_with_direct_wrapper(request_data: dict) -> dict:
    """Synthesize speech using direct F5TTSWrapper with optimizations."""
    try:
        # Use torch.no_grad() for faster inference
        with torch.no_grad():
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts_service.infer(
                ref_audio_orig=request_data["ref_audio_path"],
                ref_text=request_data.get("ref_text"),
                gen_text=request_data["gen_text"],
                model_type=request_data.get("model_type", "F5-TTS_v1"),
                remove_silence=request_data.get("remove_silence", True),
                seed=request_data.get("seed", -1),
                cross_fade_duration=request_data.get("cross_fade_duration", 0.1),
                nfe_step=request_data.get("nfe_step", 8),
                speed=request_data.get("speed", 1.0),
                show_info=logger.info,
            )

        # Save output audio
        output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(output_path, audio_data, sample_rate)

        return {
            "success": True,
            "output_path": output_path,
            "sample_rate": sample_rate,
            "spectrogram_path": spectrogram_path,
            "processed_ref_text": processed_ref_text,
            "used_seed": used_seed,
            "processing_time": 0,  # Not tracked in direct mode
            "from_cache": False
        }
    except Exception as e:
        logger.error(f"Direct wrapper synthesis failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/synthesize/")
async def synthesize_speech(
    response: Response,
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(True),  # Enable by default for faster processing
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.1),  # Reduced for faster processing
    nfe_step: Optional[int] = Form(8),  # Further reduced for faster inference
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS with Ray Serve or direct wrapper.
    Automatically uses Ray Serve if available, otherwise falls back to direct wrapper.

    Args:
        ref_audio: Reference audio file
        ref_text: Reference text (if empty, will be transcribed)
        gen_text: Text to generate
        model_type: Model type to use ("F5-TTS_v1" or "E2-TTS")
        remove_silence: Whether to remove silence from output
        seed: Random seed (-1 for random)
        cross_fade_duration: Cross-fade duration between segments
        nfe_step: Number of denoising steps
        speed: Speed multiplier
    """
    synthesis_start_time = time.time()
    request_id = f"req_{int(time.time() * 1000)}"
    logger.info(f"Endpoint '/synthesize/' called. Request ID: {request_id}")
    logger.info("Received file: %s", ref_audio.filename)
    logger.info("ref_text: %s", ref_text)
    logger.info("gen_text preview: %s", gen_text[:50])
    logger.info("Parameters - model_type: %s, remove_silence: %s, seed: %s, cross_fade_duration: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, cross_fade_duration, nfe_step, speed)
    logger.info(f"Using Ray Serve: {use_ray_serve}")

    tmp_audio_path = None
    try:
        # Save uploaded audio to temporary file (optimized)
        file_save_start = time.time()
        tmp_audio_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name

        # Use async file operations for better performance
        content = await ref_audio.read()
        with open(tmp_audio_path, 'wb') as f:
            f.write(content)

        file_save_time = time.time() - file_save_start
        logger.info("Saved reference audio to: %s (%.2fms)", tmp_audio_path, file_save_time * 1000)

        # Prepare request data with performance optimizations
        request_data = {
            "request_id": request_id,
            "ref_audio_path": tmp_audio_path,
            "ref_text": ref_text,
            "gen_text": gen_text,
            "model_type": model_type,
            "remove_silence": remove_silence,
            "seed": seed,
            "cross_fade_duration": cross_fade_duration,
            "nfe_step": max(4, min(nfe_step, 16)),  # Clamp between 4-16 for optimal speed/quality
            "speed": speed,
        }

        # Use Ray Serve or direct wrapper
        inference_start = time.time()
        if use_ray_serve:
            result = await synthesize_with_ray_serve(request_data)
        else:
            result = synthesize_with_direct_wrapper(request_data)
        inference_time = time.time() - inference_start

        # Check if synthesis was successful
        if not result.get("success", False):
            error_msg = result.get("error", "Unknown synthesis error")
            logger.error(f"Synthesis failed: {error_msg}")
            raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {error_msg}")

        # Log success information
        total_time = time.time() - synthesis_start_time
        logger.info(f"Synthesis successful for request {request_id}")
        logger.info(f"Total time: {total_time:.2f}s, Inference time: {inference_time:.2f}s")
        if "processing_time" in result:
            logger.info(f"Model processing time: {result['processing_time']:.2f}s")
        if "from_cache" in result:
            logger.info(f"Result from cache: {result['from_cache']}")
        if "used_seed" in result:
            logger.info(f"Used seed: {result['used_seed']}")
        if "processed_ref_text" in result:
            logger.info(f"Processed ref_text: {result['processed_ref_text']}")

        # Get output path
        output_path = result.get("output_path")
        if not output_path:
            # For Ray Serve, we might need to create the output file from the result
            if "audio_data" in result and "sample_rate" in result:
                output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
                sf.write(output_path, result["audio_data"], result["sample_rate"])
            else:
                raise HTTPException(status_code=500, detail="No output audio generated")

        logger.info("Generated speech saved to: %s", output_path)

        # Add performance headers
        response.headers["X-Synthesis-Time-Ms"] = str(round(total_time * 1000, 2))
        response.headers["X-Inference-Time-Ms"] = str(round(inference_time * 1000, 2))
        response.headers["X-Model-Processing-Time-Ms"] = str(round(result.get("processing_time", 0) * 1000, 2))
        response.headers["X-From-Cache"] = str(result.get("from_cache", False))
        response.headers["X-Used-Seed"] = str(result.get("used_seed", "unknown"))

        # Clean up temporary reference audio file
        try:
            if tmp_audio_path:
                os.unlink(tmp_audio_path)
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return FileResponse(output_path, media_type="audio/wav", filename="output.wav")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error during speech synthesis: %s", str(e))
        # Clean up temporary files on error
        try:
            if tmp_audio_path:
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")

@app.get("/")
def root():
    logger.info("Endpoint '/' called.")
    return {
        "message": "Welcome to the F5-TTS Web API with Ray Serve",
        "version": "3.0",
        "ray_serve_enabled": use_ray_serve,
        "features": [
            "Ray Serve integration for scalable deployment",
            "GPU-accelerated inference with 5 CPU cores",
            "Horizontal scaling and load balancing",
            "Request caching and performance optimization",
            "Optimized for speed with reduced NFE steps (4-16)",
            "Fast inference with torch.no_grad() optimization",
            "Latency tracking in response headers",
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Automatic silence removal for faster processing",
            "Concurrent request handling"
        ],
        "endpoints": {
            "/synthesize/": "Optimized synthesis with latency tracking",
            "/health": "Service health check",
            "/": "API information"
        },
        "performance": {
            "gpu_allocation": "1 GPU (cuda:0)",
            "cpu_cores": 5,
            "max_concurrent_requests": 10,
            "auto_scaling": "1-3 replicas",
            "caching_enabled": use_ray_serve,
            "optimizations": [
                "Reduced NFE steps (4-16) for faster inference",
                "torch.no_grad() for memory efficiency",
                "Automatic silence removal",
                "Optimized cross-fade duration (0.1s)",
                "Latency headers in responses"
            ]
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for the FastAPI service."""
    logger.info("Health check endpoint called.")

    health_status = {
        "status": "healthy",
        "timestamp": time.time(),
        "ray_serve_enabled": use_ray_serve,
        "service_type": "Ray Serve" if use_ray_serve else "Direct Wrapper"
    }

    # If using Ray Serve, check the deployment health
    if use_ray_serve:
        try:
            ray_health = await tts_service.health_check.remote()
            health_status["ray_serve_health"] = ray_health
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["ray_serve_error"] = str(e)

    return health_status
