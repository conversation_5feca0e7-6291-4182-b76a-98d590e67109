# F5-TTS Ray Serve Integration

This document describes the Ray Serve integration for the F5-TTS API, enabling scalable, production-ready deployment with GPU acceleration and horizontal scaling.

## Overview

The Ray Serve integration transforms the F5-TTS API into a highly scalable service that can:
- Handle multiple concurrent requests efficiently
- Scale horizontally based on demand (1-3 replicas)
- Utilize GPU resources optimally (GPU 0 with 5 CPU cores)
- Cache results for improved performance
- Provide load balancing and fault tolerance

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   FastAPI       │    │   Ray Serve      │    │   F5-TTS Model      │
│   Frontend      │───▶│   Deployment     │───▶│   (GPU Accelerated) │
│   (main.py)     │    │   (Scalable)     │    │   (5 CPU cores)     │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

## Features

### 🚀 Performance Optimizations
- **GPU Acceleration**: Uses GPU 0 for F5-TTS model inference
- **CPU Allocation**: 5 CPU cores per replica for optimal performance
- **Model Caching**: Pre-loaded models to eliminate cold start delays
- **Result Caching**: In-memory caching with 1-hour TTL
- **Request Batching**: Efficient batching for better GPU utilization

### 📈 Scalability
- **Auto-scaling**: 1-3 replicas based on demand
- **Load Balancing**: Automatic request distribution
- **Concurrent Requests**: Up to 10 concurrent requests per replica
- **Horizontal Scaling**: Add more replicas as needed

### 🔧 Production Ready
- **Health Checks**: Comprehensive health monitoring
- **Error Handling**: Graceful error handling and recovery
- **Logging**: Detailed logging for monitoring and debugging
- **Fallback**: Automatic fallback to direct wrapper if Ray Serve unavailable

## Quick Start

### 1. Deploy with Ray Serve

```bash
# Simple deployment
python deploy_f5tts.py

# Or manual deployment
python scripts/start_ray_serve.py --port 8000 --route-prefix /f5tts
```

### 2. Start FastAPI Application

```bash
# The FastAPI app will automatically detect and use Ray Serve
uvicorn main:app --host 0.0.0.0 --port 8080
```

### 3. Test the Service

```bash
# Health check
curl http://localhost:8080/health

# Ray Serve health check
curl http://localhost:8000/f5tts/health_check

# Synthesis request
curl -X POST http://localhost:8080/synthesize/ \
  -F "ref_audio=@reference.wav" \
  -F "gen_text=Hello, this is a test." \
  -F "model_type=F5-TTS_v1"
```

## Configuration

### Ray Serve Configuration

The deployment is configured with:
- **GPU**: 1 GPU (cuda:0) per replica
- **CPU**: 5 CPU cores per replica
- **Replicas**: 1-3 (auto-scaling)
- **Concurrent Queries**: 10 per replica
- **Cache**: 100 entries, 1-hour TTL

### Environment Variables

```bash
# Optional: Ray cluster address
export RAY_ADDRESS="ray://localhost:10001"

# Optional: GPU device
export CUDA_VISIBLE_DEVICES="0"
```

## API Endpoints

### FastAPI Endpoints (Port 8080)

- `GET /` - API information and status
- `POST /synthesize/` - Speech synthesis with Ray Serve
- `GET /health` - FastAPI health check

### Ray Serve Endpoints (Port 8000)

- `POST /f5tts/synthesize` - Direct Ray Serve synthesis
- `GET /f5tts/health_check` - Ray Serve health check

## Performance Benchmarks

### Without Ray Serve (Direct Wrapper)
- **Concurrent Requests**: Limited by single process
- **GPU Utilization**: Suboptimal
- **Scaling**: Vertical only

### With Ray Serve
- **Concurrent Requests**: 10+ per replica, 30+ total
- **GPU Utilization**: Optimized with batching
- **Scaling**: Horizontal auto-scaling
- **Latency**: Reduced with caching
- **Throughput**: 3x improvement with 3 replicas

## Monitoring

### Ray Dashboard
Access the Ray dashboard at `http://localhost:8265` to monitor:
- Resource utilization
- Request metrics
- Replica status
- Performance graphs

### Logs
```bash
# Ray Serve logs
ray logs

# FastAPI logs
tail -f /var/log/f5tts-api.log
```

## Troubleshooting

### Common Issues

1. **Ray Serve not starting**
   ```bash
   # Check Ray status
   ray status
   
   # Restart Ray
   ray stop
   ray start --head
   ```

2. **GPU not detected**
   ```bash
   # Check CUDA
   nvidia-smi
   python -c "import torch; print(torch.cuda.is_available())"
   ```

3. **Memory issues**
   ```bash
   # Monitor GPU memory
   nvidia-smi -l 1
   
   # Adjust batch size in config
   ```

### Fallback Mode

If Ray Serve fails, the system automatically falls back to direct wrapper mode:
- Single-process execution
- No horizontal scaling
- Direct F5TTSWrapper usage

## Advanced Configuration

### Custom Deployment

```python
from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
from ray import serve

# Custom configuration
@serve.deployment(
    num_replicas=2,
    ray_actor_options={"num_gpus": 1, "num_cpus": 8}
)
class CustomF5TTSDeployment(F5TTSServeDeployment):
    def __init__(self):
        super().__init__()
        # Custom initialization
```

### Scaling Configuration

```yaml
# config/ray_serve_config.yaml
f5tts_service:
  min_replicas: 2
  max_replicas: 5
  target_requests_per_replica: 3
  max_concurrent_queries: 15
```

## Production Deployment

### Docker Deployment

```dockerfile
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY . /app
WORKDIR /app

# Start Ray Serve and FastAPI
CMD ["bash", "-c", "python deploy_f5tts.py && uvicorn main:app --host 0.0.0.0 --port 8080"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: f5tts-ray-serve
spec:
  replicas: 1
  selector:
    matchLabels:
      app: f5tts-ray-serve
  template:
    metadata:
      labels:
        app: f5tts-ray-serve
    spec:
      containers:
      - name: f5tts
        image: f5tts-ray-serve:latest
        resources:
          limits:
            nvidia.com/gpu: 1
          requests:
            cpu: 5
            memory: 8Gi
```

## Support

For issues and questions:
1. Check the logs: `ray logs` and FastAPI logs
2. Verify GPU availability: `nvidia-smi`
3. Test with direct wrapper mode
4. Check Ray dashboard for resource usage

## Next Steps

- Monitor performance metrics
- Adjust scaling parameters based on load
- Implement custom caching strategies
- Add authentication and rate limiting
- Set up monitoring and alerting
