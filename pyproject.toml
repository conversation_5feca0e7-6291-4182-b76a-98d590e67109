[build-system]
requires = ["setuptools >= 61.0", "setuptools-scm>=8.0"]
build-backend = "setuptools.build_meta"

[project]
name = "f5-tts"
version = "1.1.5"
description = "F5-TTS: A Fairytaler that Fakes Fluent and Faithful Speech with Flow Matching"
readme = "README.md"
license = {text = "MIT License"}
requires-python = ">=3.10,<3.13" # <--- ADD THIS LINE
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]
dependencies = [
    "accelerate>=0.33.0,!=1.7.0",
    "aio-pika>=9.5.5",
    "bitsandbytes>0.37.0; platform_machine != 'arm64' and platform_system != 'Darwin'",
    "cached_path",
    "click",
    "datasets",
    "ema_pytorch>=0.5.2",
    "fastapi>=0.115.12",
    "gradio>=5.33.0",
    "hydra-core>=1.3.0",
    "jieba",
    "librosa",
    "matplotlib",
    "numpy<=1.26.4",
    "pydantic<=2.10.6",
    "pydub",
    "pypinyin",
    "python-multipart>=0.0.20",
    "ray>=2.48.0",
    "safetensors",
    "soundfile",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "torchdiffeq",
    "tqdm>=4.65.0",
    "transformers",
    "transformers_stream_generator",
    "uvicorn>=0.34.2",
    "vocos",
    "wandb",
    "x_transformers>=1.31.14",
]

[project.optional-dependencies]
eval = [
    "faster_whisper==0.10.1",
    "funasr",
    "jiwer",
    "modelscope",
    "zhconv",
    "zhon",
]

[project.urls]
Homepage = "https://github.com/SWivid/F5-TTS"

[project.scripts]
"f5-tts_infer-cli" = "f5_tts.infer.infer_cli:main"
"f5-tts_infer-gradio" = "f5_tts.infer.infer_gradio:main"
"f5-tts_finetune-cli" = "f5_tts.train.finetune_cli:main"
"f5-tts_finetune-gradio" = "f5_tts.train.finetune_gradio:main"
