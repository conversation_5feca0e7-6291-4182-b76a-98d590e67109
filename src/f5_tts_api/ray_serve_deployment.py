import asyncio
import logging
import tempfile
import os
import shutil
from typing import Dict, Any, Optional, List
import torch
import soundfile as sf
from concurrent.futures import ThreadPoolExecutor
import time
import hashlib
from functools import lru_cache
import pickle

import ray
from ray import serve
from ray.serve import deployment

from .f5tts_wrapper import F5TTSWrapper

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@serve.deployment(
    name="f5tts-service",
    ray_actor_options={
        "num_gpus": 1,  # Allocate GPU 0 for F5-TTS model inference
        "num_cpus": 5,  # Allocate 5 CPU cores as requested
    },
    autoscaling_config={
        "min_replicas": 1,
        "max_replicas": 3,  # Scale up to 3 replicas based on demand
        "target_num_ongoing_requests_per_replica": 2,
    },
    health_check_period_s=10,
    health_check_timeout_s=30,
)
class F5TTSServeDeployment:
    def __init__(self):
        """Initialize the F5-TTS Ray Serve deployment with optimizations."""
        logger.info("Initializing F5-TTS Ray Serve deployment...")
        
        # Set device to GPU 0 as requested
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize the F5TTS wrapper with GPU device
        self.tts_wrapper = F5TTSWrapper(
            model_type="F5-TTS_v1",
            device=self.device
        )
        
        # Pre-load models to avoid cold start delays
        self._preload_models()
        
        # Thread pool for CPU-bound operations (use 4 out of 5 CPU cores)
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Request batching configuration
        self.batch_size = 4
        self.batch_timeout = 0.1  # 100ms timeout for batching
        self.pending_requests = []
        self.batch_lock = asyncio.Lock()

        # Caching for improved performance
        self.result_cache = {}  # In-memory cache for results
        self.cache_max_size = 100
        self.cache_ttl = 3600  # 1 hour TTL

        # Performance optimizations
        self.warmup_completed = False

        # GPU memory optimization
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            # Set memory fraction to avoid OOM
            torch.cuda.set_per_process_memory_fraction(0.9)

        # Async warmup to avoid blocking initialization
        asyncio.create_task(self._async_warmup())

        logger.info("F5-TTS Ray Serve deployment initialized successfully")
    
    def _preload_models(self):
        """Pre-load models to reduce cold start time."""
        logger.info("Pre-loading F5-TTS models...")
        try:
            # Load vocoder
            self.tts_wrapper._load_vocoder()
            # Load default F5-TTS model
            self.tts_wrapper.get_model("F5-TTS_v1")
            logger.info("Models pre-loaded successfully")
        except Exception as e:
            logger.error(f"Error pre-loading models: {e}")
            raise

    def _warmup_model(self):
        """Warm up the model with a dummy inference to optimize performance."""
        try:
            logger.info("Warming up F5-TTS model...")
            # Create a small dummy audio file for warmup
            dummy_audio_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            dummy_audio = torch.zeros(1, 16000)  # 1 second of silence at 16kHz
            sf.write(dummy_audio_path, dummy_audio.numpy().T, 16000)

            # Perform a quick dummy inference
            self.tts_wrapper.infer(
                ref_audio_orig=dummy_audio_path,
                ref_text="Hello",
                gen_text="Test",
                model_type="F5-TTS_v1",
                nfe_step=8,  # Use fewer steps for warmup
                show_info=lambda x: None  # Silent
            )

            # Clean up
            os.unlink(dummy_audio_path)
            self.warmup_completed = True
            logger.info("Model warmup completed successfully")

        except Exception as e:
            logger.warning(f"Model warmup failed (non-critical): {e}")

    async def _async_warmup(self):
        """Asynchronous model warmup to avoid blocking initialization."""
        try:
            await asyncio.sleep(1)  # Let initialization complete first
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.thread_pool, self._warmup_model)
        except Exception as e:
            logger.warning(f"Async warmup failed: {e}")

    def _generate_cache_key(self, request_data: Dict[str, Any]) -> str:
        """Generate a cache key for the request."""
        # Create a hash of the important parameters
        cache_params = {
            "ref_text": request_data.get("ref_text", ""),
            "gen_text": request_data["gen_text"],
            "model_type": request_data.get("model_type", "F5-TTS_v1"),
            "seed": request_data.get("seed", -1),
            "nfe_step": request_data.get("nfe_step", 32),
            "speed": request_data.get("speed", 1.0),
            "cross_fade_duration": request_data.get("cross_fade_duration", 0.15),
        }

        # Include audio file hash if available
        if "ref_audio_path" in request_data:
            try:
                with open(request_data["ref_audio_path"], "rb") as f:
                    audio_hash = hashlib.md5(f.read()).hexdigest()[:8]
                cache_params["audio_hash"] = audio_hash
            except:
                pass

        cache_string = pickle.dumps(cache_params, protocol=pickle.HIGHEST_PROTOCOL)
        return hashlib.md5(cache_string).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if available and not expired."""
        if cache_key in self.result_cache:
            cached_data = self.result_cache[cache_key]
            if time.time() - cached_data["timestamp"] < self.cache_ttl:
                logger.info(f"Cache hit for key: {cache_key[:8]}...")
                return cached_data["result"]
            else:
                # Remove expired cache entry
                del self.result_cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache the result with timestamp."""
        # Implement LRU-style cache eviction
        if len(self.result_cache) >= self.cache_max_size:
            # Remove oldest entry
            oldest_key = min(self.result_cache.keys(),
                           key=lambda k: self.result_cache[k]["timestamp"])
            del self.result_cache[oldest_key]

        self.result_cache[cache_key] = {
            "result": result,
            "timestamp": time.time()
        }
        logger.info(f"Cached result for key: {cache_key[:8]}...")
    
    async def _process_batch(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of requests for better GPU utilization."""
        if not requests:
            return []
        
        logger.info(f"Processing batch of {len(requests)} requests")
        results = []
        
        # Process requests in parallel using thread pool
        loop = asyncio.get_event_loop()
        tasks = []
        
        for request in requests:
            task = loop.run_in_executor(
                self.thread_pool,
                self._process_single_request,
                request
            )
            tasks.append(task)
        
        # Wait for all requests to complete
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                logger.error(f"Error processing request {i}: {result}")
                results.append({
                    "success": False,
                    "error": str(result),
                    "request_id": requests[i].get("request_id", f"batch_{i}")
                })
            else:
                results.append(result)
        
        return results
    
    def _process_single_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single TTS request."""
        try:
            start_time = time.time()
            
            # Extract parameters from request
            ref_audio_path = request_data["ref_audio_path"]
            ref_text = request_data.get("ref_text")
            gen_text = request_data["gen_text"]
            model_type = request_data.get("model_type", "F5-TTS_v1")
            remove_silence = request_data.get("remove_silence", False)
            seed = request_data.get("seed", -1)
            cross_fade_duration = request_data.get("cross_fade_duration", 0.15)
            nfe_step = request_data.get("nfe_step", 32)
            speed = request_data.get("speed", 1.0)
            request_id = request_data.get("request_id", "unknown")
            
            logger.info(f"Processing request {request_id} with model {model_type}")
            
            # Perform inference using the wrapper
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.tts_wrapper.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )
            
            # Save output audio to temporary file
            output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            sf.write(output_path, audio_data, sample_rate)
            
            processing_time = time.time() - start_time
            logger.info(f"Request {request_id} completed in {processing_time:.2f}s")
            
            return {
                "success": True,
                "output_path": output_path,
                "sample_rate": sample_rate,
                "spectrogram_path": spectrogram_path,
                "processed_ref_text": processed_ref_text,
                "used_seed": used_seed,
                "processing_time": processing_time,
                "request_id": request_id
            }
            
        except Exception as e:
            logger.error(f"Error processing request {request_data.get('request_id', 'unknown')}: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request_data.get("request_id", "unknown")
            }
    
    async def synthesize(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main endpoint for speech synthesis with caching and optimization.

        Args:
            request_data: Dictionary containing synthesis parameters

        Returns:
            Dictionary with synthesis results
        """
        request_id = request_data.get("request_id", f"req_{int(time.time() * 1000)}")
        request_data["request_id"] = request_id

        logger.info(f"Received synthesis request {request_id}")

        # Generate cache key and check cache
        cache_key = self._generate_cache_key(request_data)
        cached_result = self._get_cached_result(cache_key)

        if cached_result:
            logger.info(f"Returning cached result for request {request_id}")
            cached_result["request_id"] = request_id
            cached_result["from_cache"] = True
            return cached_result

        # Process the request
        result = await asyncio.get_event_loop().run_in_executor(
            self.thread_pool,
            self._process_single_request,
            request_data
        )

        # Cache successful results
        if result.get("success", False):
            # Create a copy for caching (without file paths that might be temporary)
            cache_result = result.copy()
            cache_result.pop("output_path", None)  # Don't cache file paths
            self._cache_result(cache_key, cache_result)

        result["from_cache"] = False
        return result
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check endpoint for Ray Serve."""
        try:
            # Simple health check - verify GPU is available and models are loaded
            gpu_available = torch.cuda.is_available() and torch.cuda.device_count() > 0
            models_loaded = (
                self.tts_wrapper.vocoder is not None and 
                self.tts_wrapper.f5tts_model is not None
            )
            
            return {
                "status": "healthy" if gpu_available and models_loaded else "unhealthy",
                "gpu_available": gpu_available,
                "models_loaded": models_loaded,
                "device": str(self.device),
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }


# Deployment configuration function
def create_f5tts_deployment():
    """Create and return the F5-TTS Ray Serve deployment."""
    return F5TTSServeDeployment.bind()


# For direct deployment
if __name__ == "__main__":
    # Initialize Ray if not already initialized
    if not ray.is_initialized():
        ray.init()
    
    # Deploy the service
    serve.start(detached=True)
    deployment = create_f5tts_deployment()
    serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
    
    logger.info("F5-TTS Ray Serve deployment started successfully")
