import asyncio
import logging
import tempfile
import os
import shutil
from typing import Dict, Any, Optional, List
import torch
import soundfile as sf
from concurrent.futures import ThreadPoolExecutor
import time

import ray
from ray import serve
from ray.serve import deployment

from .f5tts_wrapper import F5TTSWrapper

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@serve.deployment(
    name="f5tts-service",
    num_replicas=1,
    ray_actor_options={
        "num_gpus": 1,  # Allocate 1 GPU per replica
        "num_cpus": 2,  # Allocate 2 CPUs per replica
        "resources": {"GPU": 5}  # Use 5 GPU cores as requested
    },
    max_concurrent_queries=10,  # Handle up to 10 concurrent requests
    autoscaling_config={
        "min_replicas": 1,
        "max_replicas": 3,  # Scale up to 3 replicas based on demand
        "target_num_ongoing_requests_per_replica": 2,
    },
    health_check_period_s=10,
    health_check_timeout_s=30,
)
class F5TTSServeDeployment:
    def __init__(self):
        """Initialize the F5-TTS Ray Serve deployment with optimizations."""
        logger.info("Initializing F5-TTS Ray Serve deployment...")
        
        # Set device to GPU 0 as requested
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize the F5TTS wrapper with GPU device
        self.tts_wrapper = F5TTSWrapper(
            model_type="F5-TTS_v1",
            device=self.device
        )
        
        # Pre-load models to avoid cold start delays
        self._preload_models()
        
        # Thread pool for CPU-bound operations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Request batching configuration
        self.batch_size = 4
        self.batch_timeout = 0.1  # 100ms timeout for batching
        self.pending_requests = []
        self.batch_lock = asyncio.Lock()
        
        logger.info("F5-TTS Ray Serve deployment initialized successfully")
    
    def _preload_models(self):
        """Pre-load models to reduce cold start time."""
        logger.info("Pre-loading F5-TTS models...")
        try:
            # Load vocoder
            self.tts_wrapper._load_vocoder()
            # Load default F5-TTS model
            self.tts_wrapper.get_model("F5-TTS_v1")
            logger.info("Models pre-loaded successfully")
        except Exception as e:
            logger.error(f"Error pre-loading models: {e}")
            raise
    
    async def _process_batch(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of requests for better GPU utilization."""
        if not requests:
            return []
        
        logger.info(f"Processing batch of {len(requests)} requests")
        results = []
        
        # Process requests in parallel using thread pool
        loop = asyncio.get_event_loop()
        tasks = []
        
        for request in requests:
            task = loop.run_in_executor(
                self.thread_pool,
                self._process_single_request,
                request
            )
            tasks.append(task)
        
        # Wait for all requests to complete
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                logger.error(f"Error processing request {i}: {result}")
                results.append({
                    "success": False,
                    "error": str(result),
                    "request_id": requests[i].get("request_id", f"batch_{i}")
                })
            else:
                results.append(result)
        
        return results
    
    def _process_single_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single TTS request."""
        try:
            start_time = time.time()
            
            # Extract parameters from request
            ref_audio_path = request_data["ref_audio_path"]
            ref_text = request_data.get("ref_text")
            gen_text = request_data["gen_text"]
            model_type = request_data.get("model_type", "F5-TTS_v1")
            remove_silence = request_data.get("remove_silence", False)
            seed = request_data.get("seed", -1)
            cross_fade_duration = request_data.get("cross_fade_duration", 0.15)
            nfe_step = request_data.get("nfe_step", 32)
            speed = request_data.get("speed", 1.0)
            request_id = request_data.get("request_id", "unknown")
            
            logger.info(f"Processing request {request_id} with model {model_type}")
            
            # Perform inference using the wrapper
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.tts_wrapper.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )
            
            # Save output audio to temporary file
            output_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
            sf.write(output_path, audio_data, sample_rate)
            
            processing_time = time.time() - start_time
            logger.info(f"Request {request_id} completed in {processing_time:.2f}s")
            
            return {
                "success": True,
                "output_path": output_path,
                "sample_rate": sample_rate,
                "spectrogram_path": spectrogram_path,
                "processed_ref_text": processed_ref_text,
                "used_seed": used_seed,
                "processing_time": processing_time,
                "request_id": request_id
            }
            
        except Exception as e:
            logger.error(f"Error processing request {request_data.get('request_id', 'unknown')}: {e}")
            return {
                "success": False,
                "error": str(e),
                "request_id": request_data.get("request_id", "unknown")
            }
    
    async def synthesize(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main endpoint for speech synthesis with batching support.
        
        Args:
            request_data: Dictionary containing synthesis parameters
            
        Returns:
            Dictionary with synthesis results
        """
        request_id = request_data.get("request_id", f"req_{int(time.time() * 1000)}")
        request_data["request_id"] = request_id
        
        logger.info(f"Received synthesis request {request_id}")
        
        # For now, process single requests directly for simplicity
        # In production, you might want to implement proper batching
        result = await asyncio.get_event_loop().run_in_executor(
            self.thread_pool,
            self._process_single_request,
            request_data
        )
        
        return result
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check endpoint for Ray Serve."""
        try:
            # Simple health check - verify GPU is available and models are loaded
            gpu_available = torch.cuda.is_available() and torch.cuda.device_count() > 0
            models_loaded = (
                self.tts_wrapper.vocoder is not None and 
                self.tts_wrapper.f5tts_model is not None
            )
            
            return {
                "status": "healthy" if gpu_available and models_loaded else "unhealthy",
                "gpu_available": gpu_available,
                "models_loaded": models_loaded,
                "device": str(self.device),
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }


# Deployment configuration function
def create_f5tts_deployment():
    """Create and return the F5-TTS Ray Serve deployment."""
    return F5TTSServeDeployment.bind()


# For direct deployment
if __name__ == "__main__":
    # Initialize Ray if not already initialized
    if not ray.is_initialized():
        ray.init()
    
    # Deploy the service
    serve.start(detached=True)
    deployment = create_f5tts_deployment()
    serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
    
    logger.info("F5-TTS Ray Serve deployment started successfully")
