#!/usr/bin/env python3
"""
Simple deployment script for F5-TTS Ray Serve service.
This script provides an easy way to deploy the F5-TTS service with Ray Serve.
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if required dependencies are installed."""
    logger.info("Checking dependencies...")
    
    try:
        import ray
        import torch
        logger.info(f"Ray version: {ray.__version__}")
        logger.info(f"PyTorch version: {torch.__version__}")
        logger.info(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"CUDA devices: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        return False
    
    return True


def install_dependencies():
    """Install dependencies using pip."""
    logger.info("Installing dependencies...")
    
    try:
        # Install the package in development mode
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-e", "."
        ], check=True, cwd=Path(__file__).parent)
        
        logger.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False


def start_ray_serve():
    """Start the Ray Serve deployment."""
    logger.info("Starting F5-TTS Ray Serve deployment...")
    
    script_path = Path(__file__).parent / "scripts" / "start_ray_serve.py"
    
    try:
        # Make the script executable
        os.chmod(script_path, 0o755)
        
        # Start the Ray Serve deployment
        subprocess.run([
            sys.executable, str(script_path),
            "--port", "8000",
            "--route-prefix", "/f5tts",
            "--detached"
        ], check=True)
        
        logger.info("F5-TTS Ray Serve deployment started successfully!")
        logger.info("Service endpoints:")
        logger.info("  - Synthesis: http://localhost:8000/f5tts/synthesize")
        logger.info("  - Health check: http://localhost:8000/f5tts/health_check")
        logger.info("  - Ray dashboard: http://localhost:8265")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start Ray Serve: {e}")
        return False
    except FileNotFoundError:
        logger.error(f"Startup script not found: {script_path}")
        return False


def main():
    """Main deployment function."""
    logger.info("F5-TTS Ray Serve Deployment")
    logger.info("=" * 40)
    
    # Check if dependencies are installed
    if not check_dependencies():
        logger.info("Installing missing dependencies...")
        if not install_dependencies():
            logger.error("Failed to install dependencies. Exiting.")
            sys.exit(1)
    
    # Start Ray Serve deployment
    if start_ray_serve():
        logger.info("Deployment completed successfully!")
        logger.info("\nTo test the service, you can use:")
        logger.info("curl -X POST http://localhost:8000/f5tts/health_check")
        logger.info("\nTo stop the service:")
        logger.info("ray stop")
    else:
        logger.error("Deployment failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
